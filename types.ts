export interface User {
  id: string;
  username: string;
  avatarUrl: string;
  isActive: boolean;
  isPendingApproval: boolean;
  isAdmin?: boolean; // Admin flag
  bio?: string; // User bio (up to 5 lines)
  followers?: string[]; // Array of user IDs who follow this user
  following?: string[]; // Array of user IDs this user follows
}

export interface Comment {
  id: string;
  userId: string;
  username: string;
  avatarUrl: string;
  text: string;
  timestamp: string; // ISO string
  likes?: number;
  likedUsers?: string[]; // Array of user IDs who liked the comment
  isLikedByCurrentUser?: boolean; // For UI state
  // Threading support
  parentId?: string; // ID of parent comment if this is a reply
  replyToUsername?: string; // Username being replied to for auto-quoting
  depth?: number; // Nesting depth for visual indentation
  replies?: Comment[]; // Nested replies for tree structure
}

export interface Post {
  id: string;
  userId: string;
  username: string;
  userAvatarUrl: string;
  imageUrl: string;
  caption: string;
  contentBody?: string; // Added: Main content of the post
  likes: number;
  likedUsers?: string[]; // Array of user IDs who liked the post
  isLikedByCurrentUser?: boolean; // For UI state
  comments: Comment[];
  timestamp: string; // ISO string
  tags?: string[];
  isAnonymous?: boolean; // For anonymous posts
  actualUserId?: string; // Real user ID for anonymous posts (admin only)
}

export const ADMIN_USER_ID = 'admin_s3kt0r_truth';

export interface LoginScreenConfig {
  message: string;
  imageUrl: string;
  imageOverlayOpacity?: number; // 0 to 1
}

export interface AdminMessage {
  id: string;
  senderName: string;
  senderEmail?: string;
  subject: string;
  message: string;
  timestamp: string;
  isRead: boolean;
}

// Messaging System Types
export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderUsername: string;
  senderAvatarUrl: string;
  content: string;
  timestamp: string; // ISO string
  status: MessageStatus;
  isRead: boolean;
  readBy?: string[]; // Array of user IDs who have read the message
}

export interface Conversation {
  id: string;
  participants: string[]; // Array of user IDs
  participantDetails: ConversationParticipant[]; // User details for display
  lastMessage?: Message;
  lastActivity: string; // ISO string
  unreadCount: { [userId: string]: number }; // Unread count per user
  isArchived?: boolean;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
  deletedBy?: string[]; // Array of user IDs who have deleted this conversation (for themselves only)
}

export interface ConversationParticipant {
  id: string;
  username: string;
  avatarUrl: string;
  isActive: boolean;
}

export enum MessageStatus {
  SENDING = 'sending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed'
}
