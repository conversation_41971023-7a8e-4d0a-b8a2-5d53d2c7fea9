
import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { APP_NAME } from '../constants';

const LoginPage: React.FC = () => {
  const { loginScreenConfig, login } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | React.ReactNode>('');

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      await login(email, password);
      navigate('/'); // Navigate to home after successful login
    } catch (error: any) {
      console.error('Login error:', error);
      const errorMessage = error.message || 'Login failed. Please check your credentials.';

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 relative text-neutral-100">
      {/* Background Image */}
      {loginScreenConfig.imageUrl && (
        <div
          className="absolute inset-0 w-full h-full bg-cover bg-center z-0"
          style={{ backgroundImage: `url(${loginScreenConfig.imageUrl})` }}
        >
          <div
            className="absolute inset-0 w-full h-full bg-neutral-base"
            style={{ opacity: loginScreenConfig.imageOverlayOpacity ?? 0.5 }}
          ></div>
        </div>
      )}

      {/* Matrix rain will be behind this due to z-indexing from index.html */}

      <div className="relative z-10 flex flex-col items-center bg-neutral-surface/80 backdrop-blur-md p-8 rounded-xl shadow-2xl border border-neutral-border max-w-lg w-full animate-pulse-glow">
        <h1 className="text-5xl font-logo text-brand-primary mb-4 animate-text-glow">{APP_NAME}</h1>
        <p className="text-neutral-200 mb-8 text-center text-lg">
          {loginScreenConfig.message}
        </p>

        {/* Main Login Form */}
        <form onSubmit={handleFormSubmit} className="w-full space-y-4 mb-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-neutral-300 mb-1">
              Email
            </label>
            <input
              type="text"
              id="email"
              value={email}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
              placeholder="Enter your email"
              className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary transition-all duration-200 hover:border-brand-primary/50"
              required
            />
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-neutral-300 mb-1">
              Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPassword(e.target.value)}
              placeholder="Enter your password"
              className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary transition-all duration-200 hover:border-brand-primary/50"
              required
            />
          </div>

          {error && (
            <div className="text-red-400 text-sm text-center bg-red-900/20 border border-red-500/30 rounded-md p-2">
              {error}
            </div>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-brand-primary hover:bg-brand-secondary disabled:bg-neutral-muted disabled:cursor-not-allowed text-white font-semibold py-2.5 px-4 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-surface focus:ring-brand-secondary transform hover:scale-105 hover:shadow-lg hover:shadow-brand-primary/25 disabled:transform-none disabled:shadow-none"
          >
            {isLoading ? 'Logging in...' : 'Login'}
          </button>
        </form>

        <p className="text-neutral-300 text-sm mb-4">
          Need an identity?{' '}
          <Link to="/signup" className="font-medium text-brand-primary hover:text-brand-secondary hover:underline transition-colors duration-200">
            Sign Up Here
          </Link>
        </p>



        <p className="text-xs text-neutral-muted mt-6">&copy; {new Date().getFullYear()} {APP_NAME}</p>
      </div>
    </div>
  );
};

export default LoginPage;
