import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { usePosts } from '../hooks/usePosts';
import { Post } from '../types';
import { uploadImage } from '../services/firebaseService';

type ImageInputType = 'url' | 'upload';

const EditPostPage: React.FC = () => {
  const { postId } = useParams<{ postId: string }>();
  const { currentUser, isAdmin } = useAuth();
  const { getPostById, editPost } = usePosts();
  const navigate = useNavigate();

  const [postToEdit, setPostToEdit] = useState<Post | null | undefined>(null);

  const [imageInputType, setImageInputType] = useState<ImageInputType>('url');
  const [imageUrl, setImageUrl] = useState('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const [caption, setCaption] = useState('');
  const [contentBody, setContentBody] = useState('');
  const [tags, setTags] = useState('');

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);


  useEffect(() => {
    if (!postId) {
      navigate('/');
      return;
    }
    const foundPost = getPostById(postId);
    setPostToEdit(foundPost);
    setIsLoading(false);

    if (foundPost) {
      if (!currentUser || (currentUser.id !== foundPost.userId && !isAdmin)) {
        setError("You don't have permission to edit this post.");
        return;
      }
      // Determine if existing imageUrl is a data URL (uploaded) or external URL
      if (foundPost.imageUrl.startsWith('data:image')) {
        setImageInputType('upload');
        setImagePreview(foundPost.imageUrl); // Show existing uploaded image
      } else {
        setImageInputType('url');
      }
      setImageUrl(foundPost.imageUrl);
      setCaption(foundPost.caption);
      setContentBody(foundPost.contentBody || '');
      setTags(foundPost.tags?.join(', ') || '');
    } else {
      setError("Post not found.");
    }
  }, [postId, getPostById, currentUser, isAdmin, navigate]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        const dataUrl = reader.result as string;
        setImagePreview(dataUrl);
      };
      reader.readAsDataURL(file);
    } else {
      setImageFile(null);
      // If a file was previously selected and now cleared, reset preview
      if (imageInputType === 'upload') {
        setImagePreview(null);
        // Revert to original image if it exists
        if (postToEdit && !postToEdit.imageUrl.startsWith('data:image')) {
          setImagePreview(null);
        } else if (postToEdit && postToEdit.imageUrl.startsWith('data:image')) {
          setImagePreview(postToEdit.imageUrl);
        }
      }
    }
  };

  const handleImageInputTypeChange = (type: ImageInputType) => {
    setImageInputType(type);
    // When switching, try to preserve original data if possible, or clear
    if (type === 'url' && postToEdit && !postToEdit.imageUrl.startsWith('data:image')) {
        setImageUrl(postToEdit.imageUrl); // revert to original URL if it was one
        setImagePreview(null);
    } else if (type === 'upload' && postToEdit && postToEdit.imageUrl.startsWith('data:image')) {
        setImageUrl(postToEdit.imageUrl); // revert to original data URL
        setImagePreview(postToEdit.imageUrl);
    } else { // If switching from a type that doesn't match original, or no original
        setImageUrl('');
        setImagePreview(null);
    }
    setImageFile(null);
  };


  if (isLoading) {
    return <div className="text-center text-neutral-100 py-10">Loading post...</div>;
  }

  if (error) {
     return (
      <div className="max-w-md mx-auto mt-10 p-6 bg-neutral-surface rounded-lg shadow-xl border border-neutral-border text-center">
        <p className="text-lg text-accent-error">{error}</p>
        <button onClick={() => navigate('/')} className="mt-4 bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2 px-4 rounded-md transition-colors">
          Go Home
        </button>
      </div>
    );
  }

  if (!postToEdit) {
    return (
        <div className="max-w-md mx-auto mt-10 p-6 bg-neutral-surface rounded-lg shadow-xl border border-neutral-border text-center">
          <p className="text-lg text-neutral-100">Post not found or you do not have permission to edit it.</p>
        </div>
      );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentUser || !postToEdit) {
        alert("Error: User or post data missing.");
        return;
    }
    if (currentUser.id !== postToEdit.userId && !isAdmin) {
        alert("You don't have permission to edit this post.");
        return;
    }

    // Validate that we have an image
    if (imageInputType === 'url' && !imageUrl.trim()) {
      alert("Image URL is required.");
      return;
    }
    if (imageInputType === 'upload' && !imageFile && !postToEdit.imageUrl) {
      alert("Please select an image file to upload or provide a URL.");
      return;
    }

    if (!caption.trim() && !contentBody.trim()) {
      alert("Either a Caption or a Post Body is required.");
      return;
    }

    setIsUploading(true);
    try {
      let finalImageUrl = imageUrl;

      // If uploading a new file, upload to Firebase Storage first
      if (imageInputType === 'upload' && imageFile) {
        const timestamp = Date.now();
        const fileName = `posts/${currentUser.id}/${timestamp}_${imageFile.name}`;
        finalImageUrl = await uploadImage(imageFile, fileName);
      } else if (imageInputType === 'upload' && !imageFile) {
        // Keep the existing image if no new file was selected
        finalImageUrl = postToEdit.imageUrl;
      }

      await editPost(postToEdit.id, {
        imageUrl: finalImageUrl,
        caption,
        contentBody,
        tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
      });
      navigate('/');
    } catch (error) {
      console.error('Error updating post:', error);
      alert('Failed to update post. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="max-w-xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <h1 className="text-3xl font-bold text-brand-primary mb-8 text-center">Edit Post</h1>
      <form onSubmit={handleSubmit} className="space-y-6 bg-neutral-surface p-6 sm:p-8 rounded-lg shadow-2xl border border-neutral-border">

        <div>
          <label className="block text-sm font-medium text-neutral-300 mb-2">Image Source</label>
          <div className="flex items-center space-x-4 mb-3">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="imageInputType"
                value="url"
                checked={imageInputType === 'url'}
                onChange={() => handleImageInputTypeChange('url')}
                className="form-radio h-4 w-4 text-brand-primary bg-neutral-base border-neutral-border focus:ring-brand-primary"
              />
              <span className="text-neutral-200">Use Image URL</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="imageInputType"
                value="upload"
                checked={imageInputType === 'upload'}
                onChange={() => handleImageInputTypeChange('upload')}
                className="form-radio h-4 w-4 text-brand-primary bg-neutral-base border-neutral-border focus:ring-brand-primary"
              />
              <span className="text-neutral-200">Upload Image</span>
            </label>
          </div>

          {imageInputType === 'url' && (
            <div>
              <label htmlFor="imageUrl" className="block text-sm font-medium text-neutral-300 mb-1">
                Image URL
              </label>
              <input
                type="url"
                id="imageUrl"
                value={imageUrl}
                onChange={(e) => { setImageUrl(e.target.value); if(imageInputType === 'url') setImagePreview(null);}}
                placeholder="https://example.com/image.jpg"
                className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
              />
            </div>
          )}

          {imageInputType === 'upload' && (
            <div>
              <label htmlFor="imageUpload" className="block text-sm font-medium text-neutral-300 mb-1">
                Upload New Image File (optional)
              </label>
              <input
                type="file"
                id="imageUpload"
                accept="image/*"
                onChange={handleFileChange}
                className="w-full text-sm text-neutral-300 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-brand-primary file:text-white hover:file:bg-brand-secondary cursor-pointer"
              />
              <p className="mt-1 text-xs text-neutral-muted">If you upload a new image, it will replace the current one.</p>
            </div>
          )}

          {(imagePreview || (imageUrl && imageInputType === 'url')) && (
            <div className="mt-4">
              <p className="text-sm font-medium text-neutral-300 mb-1">Current Image Preview:</p>
              <img
                src={imagePreview || imageUrl}
                alt="Preview"
                className="max-h-48 w-auto rounded-md border border-neutral-border"
                onError={(e) => { if (imageInputType === 'url') e.currentTarget.style.display = 'none';}}
                onLoad={(e) => { if (imageInputType === 'url') e.currentTarget.style.display = 'block';}}
              />
            </div>
          )}
        </div>

        <div>
          <label htmlFor="caption" className="block text-sm font-medium text-neutral-300 mb-1">
            Caption (Short Summary)
          </label>
          <input
            id="caption"
            value={caption}
            onChange={(e) => setCaption(e.target.value)}
            maxLength={150}
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>

        <div>
          <label htmlFor="contentBody" className="block text-sm font-medium text-neutral-300 mb-1">
            Post Body
          </label>
          <textarea
            id="contentBody"
            value={contentBody}
            onChange={(e) => setContentBody(e.target.value)}
            rows={6}
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>

        <div>
          <label htmlFor="tags" className="block text-sm font-medium text-neutral-300 mb-1">
            Tags (comma-separated)
          </label>
          <input
            type="text"
            id="tags"
            value={tags}
            onChange={(e) => setTags(e.target.value)}
            className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
          />
        </div>

        <button
          type="submit"
          disabled={isUploading}
          className="w-full bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2.5 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-surface focus:ring-brand-secondary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isUploading ? 'Saving Changes...' : 'Save Changes'}
        </button>
      </form>
    </div>
  );
};

export default EditPostPage;